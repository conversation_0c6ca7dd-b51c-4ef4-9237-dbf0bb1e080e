package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 单据物料信息详情
 */
@Data
@TableName("document_inventory_detail")
public class DocumentInventoryDetail {

    /**
     * 主键编码
     */
    private String id;
    /**
     * 单据详情编码
     */
    private String detailCode;

    /**
     * 容器编码
     */
    private String containerCode;

    /**
     * 批次库存ID - 关联 BasicMaterialBatchInventory表的主键
     */
    private String inventoryId;
    /**
     * 批次号
     */
    private String batchCode;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 已完成数量
     */
    private Integer completedNum;

    /**
     * 批次状态：
     * 1-待质检（需要质检的批次初始状态）
     * 2-质检中（质检任务进行中）
     * 3-待处理（质检完成或无需质检，等待最终处理：入库为待入库，出库为待出库）
     * 4-已处理（批次处理完成：入库为已入库，出库为已出库）
     */
    private Integer batchStatus;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}