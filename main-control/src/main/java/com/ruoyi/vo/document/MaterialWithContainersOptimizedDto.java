package com.ruoyi.vo.document;

import lombok.Data;
import com.ruoyi.vo.warehouse.MaterialContainerInfoDto;
import com.ruoyi.vo.warehouse.BasicMaterialNumInfo;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 优化的物料及其可用容器信息DTO
 * 用于高性能查询物料信息和容器列表
 * 
 * <AUTHOR>
 */
@Data
public class MaterialWithContainersOptimizedDto {
    
    /**
     * 物料基本信息
     */
    private BasicMaterialNumInfo materialInfo;
    
    /**
     * 该物料的可用容器列表（已包含位置信息）
     */
    private List<MaterialContainerInfoDto> availableContainers;

    /**
     * 从MaterialContainerInfoDto列表转换为MaterialWithContainersOptimizedDto列表
     * 
     * @param containerInfos 容器信息列表
     * @return 转换后的DTO列表
     */
    public static List<MaterialWithContainersOptimizedDto> fromContainerInfos(List<MaterialContainerInfoDto> containerInfos) {
        // 按物料编码分组
        Map<String, List<MaterialContainerInfoDto>> groupedByMaterial = containerInfos.stream()
                .collect(Collectors.groupingBy(MaterialContainerInfoDto::getMaterialCode));
        
        return groupedByMaterial.entrySet().stream()
                .map(entry -> {
                    String materialCode = entry.getKey();
                    List<MaterialContainerInfoDto> containers = entry.getValue();
                    
                    // 从第一个容器信息中提取物料基本信息
                    MaterialContainerInfoDto firstContainer = containers.get(0);
                    BasicMaterialNumInfo materialInfo = new BasicMaterialNumInfo();
                    materialInfo.setMaterialCode(firstContainer.getMaterialCode());
                    materialInfo.setMaterialName(firstContainer.getMaterialName());
                    materialInfo.setClassifyCode(firstContainer.getClassifyCode());
                    materialInfo.setClassifyName(firstContainer.getClassifyName());
                    materialInfo.setMaterialSort(String.valueOf(firstContainer.getMaterialSort()));
                    materialInfo.setSpecifications(firstContainer.getSpecifications());
                    materialInfo.setProduceUnit(firstContainer.getProduceUnit());
                    materialInfo.setMaterialImg(firstContainer.getMaterialImg());
                    
                    // 计算总库存数量
                    int totalMaterialNum = containers.stream().mapToInt(MaterialContainerInfoDto::getMaterialNum).sum();
                    int totalFreezeNum = containers.stream().mapToInt(MaterialContainerInfoDto::getFreezeNum).sum();
                    int totalAvailNum = containers.stream().mapToInt(MaterialContainerInfoDto::getAvailNum).sum();
                    
                    materialInfo.setMaterialNum(totalMaterialNum);
                    materialInfo.setFreezeNum(totalFreezeNum);
                    materialInfo.setAvailNum(totalAvailNum);
                    
                    // 创建结果DTO
                    MaterialWithContainersOptimizedDto result = new MaterialWithContainersOptimizedDto();
                    result.setMaterialInfo(materialInfo);
                    result.setAvailableContainers(containers);
                    
                    return result;
                })
                .collect(Collectors.toList());
    }
}
