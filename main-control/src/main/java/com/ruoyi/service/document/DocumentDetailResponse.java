package com.ruoyi.service.document;

import lombok.Data;

/**
 *单据明细详情表
 */
@Data
public class DocumentDetailResponse {

    /**
     * 主键编码
     */
    private String id;
    /**
     * 父表单据编号
     */
    private String documentCode;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料编码
     */
    private String materialName;

    /**
     * 物料类型
     */
    private Integer materialSort;


    /**
     * 规格型号
     */
    private String specifications;

    /**
     * 材质
     */
    private String texture;


    /**
     * 全部数量
     */
    private Integer quantity;
    /**
     * 已完成数量
     */
    private Integer completedNum;
    /**
     * 本次数量
     */
    private Integer currentNum;


    /**
     * 单据编号
     */
    private String transactionCode;

    /**
     * 供销编码
     */
    private String supplySalesCode;
    /**
     * 总到货数量（累计所有批次的实际到货数量，包含不合格品）
     * 入库场景：总来料数量（所有批次累计到货，可能超过计划数量）
     * 出库场景：总准备出库数量（所有批次累计准备出库的数量）
     * 注：此数量可能大于等于已完成数量，因为包含不合格或未通过处理的部分
     */
    private Integer totalArrivalNum;
    /**
     * 任务状态：
     * 0-待处理（单据锁定后的初始状态，入库为待入库，出库为待出库）
     * 1-处理中（部分数量已处理，入库为部分入库，出库为部分出库）
     * 2-已完成（全部数量已处理完成，入库为入库完成，出库为出库完成）
     */
    private Integer taskStatus;
    /**
     * 最新批次状态（显示该物料明细最新一批的执行状态）：
     * 1-待质检（最新批次需要质检）
     * 2-质检中（最新批次正在质检）
     * 3-待处理（最新批次质检完成或无需质检，待出入库）
     * 4-已处理（最新批次已完成出入库）
     *
     */
    private Integer latestBatchStatus;

}